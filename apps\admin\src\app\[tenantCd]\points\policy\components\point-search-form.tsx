"use client";

import {
  applyDate<PERSON><PERSON>eP<PERSON>ms,
  DateRangePicker,
} from "@/components/date-range-picker";
import { PointSearchFilter } from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  searchType: z.string(),
  keyword: z.string().optional(),
  serviceName: z.string(),
  pointType: z.string(),
  pointDetailType: z.string(),
  serviceType: z.string(),
  earnDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
  expDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
  pageSize: z.number().optional(),
});

export const PointSearchForm = ({
  initialParams,
  isMainTenant = false,
}: {
  initialParams?: PointSearchFilter;
  isMainTenant?: boolean;
}) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    values: {
      searchType: initialParams?.searchType ?? "all",
      keyword: initialParams?.keyword ?? "",
      serviceName: initialParams?.serviceName ?? "all",
      pointType: initialParams?.pointType ?? "all",
      pointDetailType: initialParams?.pointDetailType ?? "all",
      serviceType: initialParams?.serviceType ?? "all",
      earnDt: initialParams?.earnDt,
      expDt: initialParams?.expDt,
      pageSize: initialParams?.pageSize ?? 10,
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    const searchParams = new URLSearchParams();
    if (data.keyword) {
      searchParams.set("searchType", data.searchType);
      searchParams.set("keyword", data.keyword);
    }
    if (data.serviceName !== "all") {
      searchParams.set("serviceName", data.serviceName);
    }
    if (data.pointType !== "all") {
      searchParams.set("pointType", data.pointType);
    }
    if (data.pointDetailType !== "all") {
      searchParams.set("pointDetailType", data.pointDetailType);
    }
    if (data.serviceType !== "all") {
      searchParams.set("serviceType", data.serviceType);
    }
    if (data.pageSize) {
      searchParams.set("pageSize", data.pageSize.toString());
    }
    applyDateRangeParams(searchParams, "earnDt", data.earnDt);
    applyDateRangeParams(searchParams, "expDt", data.expDt);
    router.push(`?${searchParams.toString()}`);
  });

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">검색어</Label>
              <FormField
                control={form.control}
                name="searchType"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">전체</SelectItem>
                        <SelectItem value="userNm">이름</SelectItem>
                        <SelectItem value="phone">전화번호</SelectItem>
                        <SelectItem value="birth">생년월일</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="keyword"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input placeholder="검색어를 입력하세요" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">
                포인트 충전/취소 일시
              </Label>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="expDt"
                  render={({ field }) => (
                    <DateRangePicker
                      dateRange={field.value}
                      onChange={field.onChange}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">
                포인트 충전/취소
              </Label>
              <FormField
                control={form.control}
                name="serviceType"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="all" id="serviceType-all" />
                          <Label htmlFor="serviceType-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="common"
                            id="serviceType-common"
                          />
                          <Label
                            htmlFor="serviceType-common"
                            className="text-sm"
                          >
                            충전
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="local"
                            id="serviceType-local"
                          />
                          <Label
                            htmlFor="serviceType-local"
                            className="text-sm"
                          >
                            취소
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">성공/실패</Label>
              <FormField
                control={form.control}
                name="serviceType"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="all" id="serviceType-all" />
                          <Label htmlFor="serviceType-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="common"
                            id="serviceType-common"
                          />
                          <Label
                            htmlFor="serviceType-common"
                            className="text-sm"
                          >
                            성공
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="local"
                            id="serviceType-local"
                          />
                          <Label
                            htmlFor="serviceType-local"
                            className="text-sm"
                          >
                            실패
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">충전방식</Label>
              <FormField
                control={form.control}
                name="serviceType"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="all" id="serviceType-all" />
                          <Label htmlFor="serviceType-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="common"
                            id="serviceType-common"
                          />
                          <Label
                            htmlFor="serviceType-common"
                            className="text-sm"
                          >
                            CBCD
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem
                            value="local"
                            id="serviceType-local"
                          />
                          <Label
                            htmlFor="serviceType-local"
                            className="text-sm"
                          >
                            카드충전
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">조회건수</Label>
              <FormField
                control={form.control}
                name="pageSize"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={(value) => field.onChange(Number(value))}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="10">10건</SelectItem>
                        <SelectItem value="20">20건</SelectItem>
                        <SelectItem value="50">50건</SelectItem>
                        <SelectItem value="100">100건</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-24 text-sm font-medium">지역코드</Label>
              <FormField
                control={form.control}
                name="pointDetailType"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">고정값</SelectItem>
                        <SelectItem value="seoul_kangnam">서울-강남</SelectItem>
                        <SelectItem value="gyeonggi_pt">경기-평택</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-4">
            <div className="ml-auto flex gap-4">
              <Button type="submit" className="px-6 py-2">
                검색
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </Card>
  );
};
