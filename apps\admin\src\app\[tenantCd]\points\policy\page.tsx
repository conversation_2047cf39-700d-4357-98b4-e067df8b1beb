"use client";

import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import { useState } from "react";
import { AppContent } from "../../components/container";
import { PolicyFormDialog } from "./components/policy-form-dialog";
import { PolicyList } from "./components/policy-list";

export default function PointPolicyPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<"create" | "edit">("create");
  const [selectedPolicy, setSelectedPolicy] = useState<PointPolicyItem | null>(
    null,
  );

  const handleCreateClick = () => {
    setDialogMode("create");
    setSelectedPolicy(null);
    setDialogOpen(true);
  };

  const handleEditClick = (policy: PointPolicyItem) => {
    setDialogMode("edit");
    setSelectedPolicy(policy);
    setDialogOpen(true);
  };

  return (
    <>
      <AppContent
        title="포인트 전환 정책관리"
        titleSuffix={<Button onClick={handleCreateClick}>등록</Button>}
      >
        <PolicyList onEditClick={handleEditClick} />
      </AppContent>

      <PolicyFormDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        policy={selectedPolicy}
        mode={dialogMode}
      />
    </>
  );
}
