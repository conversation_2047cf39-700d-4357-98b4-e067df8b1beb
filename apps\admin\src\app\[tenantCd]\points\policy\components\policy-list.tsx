"use client";

import { Pagination } from "@/components/pagination";
import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { PointTable } from "./point-table";

export const PolicyList = ({
  data,
  count,
  pageNumber,
  pageSize,
}: {
  data: PointPolicyItem[];
  count: number;
  pageNumber: number;
  pageSize: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };
  return (
    <div className="mt-8">
      <PointTable data={data} />
      <Pagination
        count={count}
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mt-[10px]"
      />
    </div>
  );
};
