"use client";

import { Pagination } from "@/components/pagination";
import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { PointTable } from "./point-table";

interface PolicyListProps {
  onEditClick: (policy: PointPolicyItem) => void;
}

export const PolicyList = ({ onEditClick }: PolicyListProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const page = searchParams.get("page") || "1";
  const pageNumber = parseInt(page, 10);
  const pageSize = 10;

  // 더미 데이터
  const dummyData: PointPolicyItem[] = [
    {
      srvcCd: "SVC001",
      rewardCd: "RWD001",
      rewardNm: "포인트 적립",
      rewardRate: "1.0",
      amt: "1000",
      dailyLimitCount: 5,
      monthlyLimitCount: 100,
      totalLimitCount: 1000,
      maxAmtOnce: "10000",
      maxAmtDaily: "50000",
      maxAmtMonthly: "500000",
      maxAmtTotal: "1000000",
    },
    {
      srvcCd: "SVC002",
      rewardCd: "RWD002",
      rewardNm: "포인트 사용",
      rewardRate: "0.5",
      amt: "500",
      dailyLimitCount: 3,
      monthlyLimitCount: 50,
      totalLimitCount: 500,
      maxAmtOnce: "5000",
      maxAmtDaily: "25000",
      maxAmtMonthly: "250000",
      maxAmtTotal: "500000",
    },
  ];

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };

  return (
    <div className="mt-8">
      <PointTable data={dummyData} onEditClick={onEditClick} />
      <Pagination
        count={20} // 더미 카운트
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mt-[10px]"
      />
    </div>
  );
};
