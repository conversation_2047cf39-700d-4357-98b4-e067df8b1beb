"use client";

import { Pagination } from "@/components/pagination";
import {
  getPolicyList,
  PointPolicyItem,
  readPolicyCount,
} from "@workspace/db/crud/common/points";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useEffect, useState } from "react";
import { PointTable } from "./point-table";

interface PolicyListProps {
  onEditClick: (policy: PointPolicyItem) => void;
}

export const PolicyList = ({ onEditClick }: PolicyListProps) => {
  const [data, setData] = useState<PointPolicyItem[]>([]);
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const tenantCd = params.tenantCd as string;
  const page = searchParams.get("page") || "1";
  const pageNumber = parseInt(page, 10);
  const pageSize = 10;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [policies, policyCount] = await Promise.all([
          getPolicyList(tenantCd, pageNumber, pageSize),
          readPolicyCount(tenantCd),
        ]);
        setData(policies);
        setCount(policyCount);
      } catch (error) {
        console.error("Failed to fetch policies:", error);
        setData([]);
        setCount(0);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenantCd, pageNumber, pageSize]);

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };

  if (loading) {
    return <DataTableSkeleton pageSize={pageSize} />;
  }

  return (
    <div className="mt-8">
      <PointTable data={data} onEditClick={onEditClick} />
      <Pagination
        count={count}
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mt-[10px]"
      />
    </div>
  );
};
