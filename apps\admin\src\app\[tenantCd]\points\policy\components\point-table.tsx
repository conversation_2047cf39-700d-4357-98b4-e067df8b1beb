"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Masker } from "@toss/utils";
import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTable } from "@workspace/ui/components/data-table";
import { format } from "date-fns";
import { useMemo } from "react";

const getColumnDefs = () => {
  const columnDefs: ColumnDef<PointPolicyItem>[] = [
    {
      accessorKey: "rowNumber",
      header: "순번",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value;
      },
    },
    {
      accessorKey: "rewardCd",
      header: "서비스",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },

    {
      accessorKey: "rewardNm",
      header: "사용유형",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value.slice(-8);
      },
    },
    {
      accessorKey: "dailyLimitCount",
      header: "최소단위",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return Masker.maskPhoneNumber(value);
      },
    },
    {
      accessorKey: "basicUnit",
      header: "기본단위",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value === "kona" ? "코나아이" : value || "-";
      },
    },
    {
      accessorKey: "maxAmtOnce",
      header: "일회 한도 금액",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value === "kona" ? "코나아이" : value || "-";
      },
    },
    {
      accessorKey: "maxAmtDaily",
      header: "최대 한도 금액(일)",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value.toLocaleString() + "P";
      },
    },
    {
      accessorKey: "maxAmtMonthly",
      header: "최대 한도 금액(월)",
      cell: ({ getValue }) => {
        const value = getValue() as number;
        return value.toLocaleString() + "P";
      },
    },
    {
      accessorKey: "dailyCount",
      header: "전환/교환횟수(일)",
      cell: ({ getValue }) => {
        const value = getValue() as Date;
        return format(value, "yyyy.MM.dd HH:mm");
      },
    },
    {
      accessorKey: "setting",
      header: "관리",
      cell: ({ getValue }) => {
        const value = getValue() as Date;
        return format(value, "yyyy.MM.dd HH:mm");
      },
    },
  ];

  return columnDefs;
};

export const PointTable = ({ data }: { data: PointPolicyItem[] }) => {
  const columns: ColumnDef<PointPolicyItem>[] = useMemo(
    () => getColumnDefs(),
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};
