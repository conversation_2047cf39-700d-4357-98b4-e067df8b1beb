"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import { DataTable } from "@workspace/ui/components/data-table";
import { useMemo } from "react";

const getColumnDefs = () => {
  const columnDefs: ColumnDef<PointPolicyItem>[] = [
    {
      accessorKey: "srvcCd",
      header: "서비스 코드",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      accessorKey: "rewardCd",
      header: "리워드 코드",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return value || "-";
      },
    },
    {
      accessorKey: "rewardNm",
      header: "리워드명",
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value || "-";
      },
    },
    {
      accessorKey: "rewardRate",
      header: "리워드 비율",
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value || "-";
      },
    },
    {
      accessorKey: "amt",
      header: "금액",
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? `${value}원` : "-";
      },
    },
    {
      accessorKey: "dailyLimitCount",
      header: "일일 한도 횟수",
      cell: ({ getValue }) => {
        const value = getValue() as number | null;
        return value ? `${value}회` : "-";
      },
    },
    {
      accessorKey: "monthlyLimitCount",
      header: "월간 한도 횟수",
      cell: ({ getValue }) => {
        const value = getValue() as number | null;
        return value ? `${value}회` : "-";
      },
    },
    {
      accessorKey: "maxAmtOnce",
      header: "1회 최대 금액",
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? `${value}원` : "-";
      },
    },
    {
      accessorKey: "maxAmtDaily",
      header: "일일 최대 금액",
      cell: ({ getValue }) => {
        const value = getValue() as string | null;
        return value ? `${value}원` : "-";
      },
    },
    {
      id: "actions",
      header: "관리",
      cell: ({ row }) => {
        return (
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // TODO: 수정 기능 구현
              console.log("수정:", row.original);
            }}
          >
            수정
          </Button>
        );
      },
    },
  ];

  return columnDefs;
};

export const PointTable = ({ data }: { data: PointPolicyItem[] }) => {
  const columns: ColumnDef<PointPolicyItem>[] = useMemo(
    () => getColumnDefs(),
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};
