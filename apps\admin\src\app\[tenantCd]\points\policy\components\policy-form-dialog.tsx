"use client";

import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  rewardNm: z.string().min(1, "서비스명을 입력해주세요"),
  amt: z.string().min(1, "최소단위를 입력해주세요"),
  rewardRate: z.string().min(1, "기본단위를 입력해주세요"),
  maxAmtOnce: z.string().min(1, "일회 한도 금액을 입력해주세요"),
  maxAmtDaily: z.string().min(1, "최대 한도 금액(일)을 입력해주세요"),
  maxAmtMonthly: z.string().min(1, "최대 한도 금액(월)을 입력해주세요"),
  dailyLimitCount: z.string().min(1, "전환 횟수(일)를 입력해주세요"),
});

type FormData = z.infer<typeof formSchema>;

interface PolicyFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  policy?: PointPolicyItem | null;
  mode: "create" | "edit";
}

export const PolicyFormDialog = ({
  open,
  onOpenChange,
  policy,
  mode,
}: PolicyFormDialogProps) => {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      rewardNm: policy?.rewardNm || "",
      amt: policy?.amt || "",
      rewardRate: policy?.rewardRate || "",
      maxAmtOnce: policy?.maxAmtOnce || "",
      maxAmtDaily: policy?.maxAmtDaily || "",
      maxAmtMonthly: policy?.maxAmtMonthly || "",
      dailyLimitCount: policy?.dailyLimitCount?.toString() || "",
    },
  });

  const onSubmit = (data: FormData) => {
    console.log("Form data:", data);
    // TODO: API 호출 구현
    onOpenChange(false);
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {mode === "create" ? "정책 등록" : "정책 수정"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="rewardNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>서비스</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>최소단위</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rewardRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>기본단위</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxAmtOnce"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>일회 한도 금액</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxAmtDaily"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>최대 한도 금액(일)</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxAmtMonthly"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>최대 한도 금액(월)</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dailyLimitCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>전환 횟수(일)</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                취소
              </Button>
              <Button type="submit">
                {mode === "create" ? "등록" : "수정"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
